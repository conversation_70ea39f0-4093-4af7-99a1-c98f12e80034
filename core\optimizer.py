"""
Advanced strategy optimizer using genetic algorithms and Bayesian optimization.
Focuses on robust parameter optimization with walk-forward analysis.
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Callable
from dataclasses import dataclass
import multiprocessing as mp
from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
import optuna
from deap import base, creator, tools, algorithms
import time
from loguru import logger

from strategies.base_strategy import BaseStrategy
from core.backtester import VectorizedBacktester, BacktestResult
from core.data_manager import DataManager
from config.settings import settings


@dataclass
class OptimizationResult:
    """Results from strategy optimization."""
    best_parameters: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    walk_forward_results: List[BacktestResult]
    robustness_score: float
    execution_time: float
    total_evaluations: int


class StrategyOptimizer:
    """
    Advanced strategy optimizer with multiple optimization algorithms.
    Supports genetic algorithms, Bayesian optimization, and walk-forward analysis.
    """
    
    def __init__(
        self,
        data_manager: DataManager,
        backtester: VectorizedBacktester,
        optimization_metric: str = 'sharpe_ratio'
    ):
        self.data_manager = data_manager
        self.backtester = backtester
        self.optimization_metric = optimization_metric
        
        # Optimization settings
        self.population_size = 50
        self.generations = 30
        self.mutation_rate = 0.2
        self.crossover_rate = 0.8
        
        # Walk-forward settings
        self.train_period_months = 12
        self.test_period_months = 3
        self.step_months = 1
        
    def optimize_strategy(
        self,
        strategy_class: type,
        symbol: str,
        timeframe: str,
        parameter_ranges: Dict[str, Tuple[Any, Any]],
        start_date: str,
        end_date: str,
        method: str = 'genetic',
        n_trials: int = 100
    ) -> OptimizationResult:
        """
        Optimize strategy parameters using specified method.
        
        Args:
            strategy_class: Strategy class to optimize
            symbol: Trading symbol
            timeframe: Data timeframe
            parameter_ranges: Parameter ranges for optimization
            start_date: Optimization start date
            end_date: Optimization end date
            method: Optimization method ('genetic', 'bayesian', 'grid')
            n_trials: Number of optimization trials
            
        Returns:
            OptimizationResult with best parameters and performance
        """
        start_time = time.perf_counter()
        logger.info(f"Starting {method} optimization for {strategy_class.__name__}")
        
        # Get historical data
        data = self.data_manager.get_historical_data(
            symbol, timeframe, start_date, end_date
        )
        
        if len(data) < 1000:
            raise ValueError("Insufficient data for optimization")
        
        # Choose optimization method
        if method == 'genetic':
            result = self._genetic_optimization(
                strategy_class, symbol, timeframe, parameter_ranges, data
            )
        elif method == 'bayesian':
            result = self._bayesian_optimization(
                strategy_class, symbol, timeframe, parameter_ranges, data, n_trials
            )
        elif method == 'grid':
            result = self._grid_search_optimization(
                strategy_class, symbol, timeframe, parameter_ranges, data
            )
        else:
            raise ValueError(f"Unknown optimization method: {method}")
        
        # Perform walk-forward analysis
        logger.info("Performing walk-forward analysis...")
        walk_forward_results = self._walk_forward_analysis(
            strategy_class, symbol, timeframe, result.best_parameters, data
        )
        
        # Calculate robustness score
        robustness_score = self._calculate_robustness_score(walk_forward_results)
        
        execution_time = time.perf_counter() - start_time
        
        # Update result with walk-forward analysis
        result.walk_forward_results = walk_forward_results
        result.robustness_score = robustness_score
        result.execution_time = execution_time
        
        logger.info(f"Optimization completed in {execution_time:.2f}s")
        logger.info(f"Best {self.optimization_metric}: {result.best_score:.4f}")
        logger.info(f"Robustness score: {robustness_score:.4f}")
        
        return result
    
    def _genetic_optimization(
        self,
        strategy_class: type,
        symbol: str,
        timeframe: str,
        parameter_ranges: Dict[str, Tuple[Any, Any]],
        data: pd.DataFrame
    ) -> OptimizationResult:
        """Genetic algorithm optimization."""
        
        # Setup DEAP genetic algorithm
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMax)
        
        toolbox = base.Toolbox()
        
        # Parameter encoding
        param_names = list(parameter_ranges.keys())
        param_bounds = list(parameter_ranges.values())
        
        # Register genetic operators
        for i, (param_name, (min_val, max_val)) in enumerate(parameter_ranges.items()):
            if isinstance(min_val, int):
                toolbox.register(f"attr_{i}", np.random.randint, min_val, max_val + 1)
            else:
                toolbox.register(f"attr_{i}", np.random.uniform, min_val, max_val)
        
        toolbox.register("individual", tools.initCycle, creator.Individual,
                        [getattr(toolbox, f"attr_{i}") for i in range(len(param_names))], n=1)
        toolbox.register("population", tools.initRepeat, list, toolbox.individual)
        
        # Evaluation function
        def evaluate_individual(individual):
            params = dict(zip(param_names, individual))
            try:
                strategy = strategy_class(symbol, timeframe, **params)
                result = self.backtester.run_backtest(strategy, data)
                return (getattr(result, self.optimization_metric),)
            except Exception as e:
                logger.warning(f"Evaluation failed: {e}")
                return (-np.inf,)
        
        toolbox.register("evaluate", evaluate_individual)
        toolbox.register("mate", tools.cxTwoPoint)
        toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
        toolbox.register("select", tools.selTournament, tournsize=3)
        
        # Run genetic algorithm
        population = toolbox.population(n=self.population_size)
        
        # Parallel evaluation
        if settings.parallel_processing:
            with ProcessPoolExecutor(max_workers=settings.max_workers) as executor:
                fitnesses = list(executor.map(evaluate_individual, population))
        else:
            fitnesses = [evaluate_individual(ind) for ind in population]
        
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
        
        # Evolution
        optimization_history = []
        
        for generation in range(self.generations):
            # Selection
            offspring = toolbox.select(population, len(population))
            offspring = list(map(toolbox.clone, offspring))
            
            # Crossover and mutation
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if np.random.random() < self.crossover_rate:
                    toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values
            
            for mutant in offspring:
                if np.random.random() < self.mutation_rate:
                    toolbox.mutate(mutant)
                    del mutant.fitness.values
            
            # Evaluate invalid individuals
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            if settings.parallel_processing and len(invalid_ind) > 1:
                with ProcessPoolExecutor(max_workers=settings.max_workers) as executor:
                    fitnesses = list(executor.map(evaluate_individual, invalid_ind))
            else:
                fitnesses = [evaluate_individual(ind) for ind in invalid_ind]
            
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
            
            # Replace population
            population[:] = offspring
            
            # Track progress
            fits = [ind.fitness.values[0] for ind in population]
            best_fitness = max(fits)
            avg_fitness = np.mean(fits)
            
            optimization_history.append({
                'generation': generation,
                'best_fitness': best_fitness,
                'avg_fitness': avg_fitness
            })
            
            logger.info(f"Generation {generation}: Best={best_fitness:.4f}, Avg={avg_fitness:.4f}")
        
        # Get best individual
        best_individual = tools.selBest(population, 1)[0]
        best_parameters = dict(zip(param_names, best_individual))
        best_score = best_individual.fitness.values[0]
        
        return OptimizationResult(
            best_parameters=best_parameters,
            best_score=best_score,
            optimization_history=optimization_history,
            walk_forward_results=[],
            robustness_score=0.0,
            execution_time=0.0,
            total_evaluations=self.population_size * (self.generations + 1)
        )
    
    def _bayesian_optimization(
        self,
        strategy_class: type,
        symbol: str,
        timeframe: str,
        parameter_ranges: Dict[str, Tuple[Any, Any]],
        data: pd.DataFrame,
        n_trials: int
    ) -> OptimizationResult:
        """Bayesian optimization using Optuna."""
        
        def objective(trial):
            params = {}
            for param_name, (min_val, max_val) in parameter_ranges.items():
                if isinstance(min_val, int):
                    params[param_name] = trial.suggest_int(param_name, min_val, max_val)
                else:
                    params[param_name] = trial.suggest_float(param_name, min_val, max_val)
            
            try:
                strategy = strategy_class(symbol, timeframe, **params)
                result = self.backtester.run_backtest(strategy, data)
                return getattr(result, self.optimization_metric)
            except Exception as e:
                logger.warning(f"Trial failed: {e}")
                return -np.inf
        
        # Create study
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials)
        
        # Extract results
        optimization_history = []
        for trial in study.trials:
            optimization_history.append({
                'trial': trial.number,
                'value': trial.value if trial.value is not None else -np.inf,
                'parameters': trial.params
            })
        
        return OptimizationResult(
            best_parameters=study.best_params,
            best_score=study.best_value,
            optimization_history=optimization_history,
            walk_forward_results=[],
            robustness_score=0.0,
            execution_time=0.0,
            total_evaluations=n_trials
        )
    
    def _grid_search_optimization(
        self,
        strategy_class: type,
        symbol: str,
        timeframe: str,
        parameter_ranges: Dict[str, Tuple[Any, Any]],
        data: pd.DataFrame
    ) -> OptimizationResult:
        """Grid search optimization."""
        
        # Generate parameter grid
        param_grids = []
        param_names = list(parameter_ranges.keys())
        
        for param_name, (min_val, max_val) in parameter_ranges.items():
            if isinstance(min_val, int):
                grid = list(range(min_val, max_val + 1, max(1, (max_val - min_val) // 10)))
            else:
                grid = np.linspace(min_val, max_val, 11).tolist()
            param_grids.append(grid)
        
        # Generate all combinations
        from itertools import product
        param_combinations = list(product(*param_grids))
        
        logger.info(f"Testing {len(param_combinations)} parameter combinations")
        
        # Evaluate all combinations
        results = []
        
        def evaluate_params(params_tuple):
            params = dict(zip(param_names, params_tuple))
            try:
                strategy = strategy_class(symbol, timeframe, **params)
                result = self.backtester.run_backtest(strategy, data)
                return params, getattr(result, self.optimization_metric)
            except Exception as e:
                logger.warning(f"Evaluation failed for {params}: {e}")
                return params, -np.inf
        
        if settings.parallel_processing:
            with ProcessPoolExecutor(max_workers=settings.max_workers) as executor:
                results = list(executor.map(evaluate_params, param_combinations))
        else:
            results = [evaluate_params(params) for params in param_combinations]
        
        # Find best result
        best_params, best_score = max(results, key=lambda x: x[1])
        
        # Create optimization history
        optimization_history = []
        for i, (params, score) in enumerate(results):
            optimization_history.append({
                'iteration': i,
                'parameters': params,
                'score': score
            })
        
        return OptimizationResult(
            best_parameters=best_params,
            best_score=best_score,
            optimization_history=optimization_history,
            walk_forward_results=[],
            robustness_score=0.0,
            execution_time=0.0,
            total_evaluations=len(param_combinations)
        )
    
    def _walk_forward_analysis(
        self,
        strategy_class: type,
        symbol: str,
        timeframe: str,
        parameters: Dict[str, Any],
        data: pd.DataFrame
    ) -> List[BacktestResult]:
        """Perform walk-forward analysis for robustness testing."""
        
        results = []
        
        # Calculate date ranges
        start_date = data.index[0]
        end_date = data.index[-1]
        
        current_date = start_date
        
        while current_date < end_date:
            # Define train and test periods
            train_start = current_date
            train_end = current_date + pd.DateOffset(months=self.train_period_months)
            test_start = train_end
            test_end = test_start + pd.DateOffset(months=self.test_period_months)
            
            if test_end > end_date:
                break
            
            # Get data for periods
            train_data = data[train_start:train_end]
            test_data = data[test_start:test_end]
            
            if len(train_data) < 100 or len(test_data) < 30:
                current_date += pd.DateOffset(months=self.step_months)
                continue
            
            try:
                # Create strategy with optimized parameters
                strategy = strategy_class(symbol, timeframe, **parameters)
                
                # Run backtest on test period
                result = self.backtester.run_backtest(strategy, test_data)
                results.append(result)
                
                logger.info(f"Walk-forward period {test_start.date()} to {test_end.date()}: "
                           f"{self.optimization_metric}={getattr(result, self.optimization_metric):.4f}")
                
            except Exception as e:
                logger.warning(f"Walk-forward analysis failed for period {test_start} to {test_end}: {e}")
            
            current_date += pd.DateOffset(months=self.step_months)
        
        return results
    
    def _calculate_robustness_score(self, walk_forward_results: List[BacktestResult]) -> float:
        """Calculate robustness score based on walk-forward results."""
        if not walk_forward_results:
            return 0.0
        
        # Extract metric values
        metric_values = [getattr(result, self.optimization_metric) for result in walk_forward_results]
        
        if not metric_values:
            return 0.0
        
        # Calculate robustness metrics
        mean_performance = np.mean(metric_values)
        std_performance = np.std(metric_values)
        positive_periods = sum(1 for value in metric_values if value > 0)
        consistency_ratio = positive_periods / len(metric_values)
        
        # Robustness score combines mean performance, consistency, and stability
        stability_score = 1.0 / (1.0 + std_performance) if std_performance > 0 else 1.0
        robustness_score = mean_performance * consistency_ratio * stability_score
        
        return max(0.0, robustness_score)
