"""
Global configuration settings for the trading framework.
"""
import os
from typing import Dict, Any
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    """Global settings configuration."""
    
    # Binance API
    binance_api_key: str = Field(..., env="BINANCE_API_KEY")
    binance_api_secret: str = Field(..., env="BINANCE_API_SECRET")
    binance_testnet: bool = Field(True, env="BINANCE_TESTNET")
    
    # Database
    database_url: str = Field("sqlite:///trading_data.db", env="DATABASE_URL")
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    
    # Trading
    default_symbol: str = Field("BTCUSDT", env="DEFAULT_SYMBOL")
    default_interval: str = Field("1h", env="DEFAULT_INTERVAL")
    max_concurrent_strategies: int = Field(10, env="MAX_CONCURRENT_STRATEGIES")
    backtest_start_date: str = Field("2020-01-01", env="BACKTEST_START_DATE")
    backtest_end_date: str = Field("2024-01-01", env="BACKTEST_END_DATE")
    
    # Performance
    enable_jit_compilation: bool = Field(True, env="ENABLE_JIT_COMPILATION")
    cache_enabled: bool = Field(True, env="CACHE_ENABLED")
    parallel_processing: bool = Field(True, env="PARALLEL_PROCESSING")
    max_workers: int = Field(8, env="MAX_WORKERS")
    
    # Risk Management
    max_position_size: float = Field(0.1, env="MAX_POSITION_SIZE")
    stop_loss_percentage: float = Field(0.02, env="STOP_LOSS_PERCENTAGE")
    take_profit_percentage: float = Field(0.06, env="TAKE_PROFIT_PERCENTAGE")
    max_drawdown: float = Field(0.15, env="MAX_DRAWDOWN")
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/trading.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Trading intervals mapping
INTERVALS = {
    "1m": "1m",
    "3m": "3m",
    "5m": "5m",
    "15m": "15m",
    "30m": "30m",
    "1h": "1h",
    "2h": "2h",
    "4h": "4h",
    "6h": "6h",
    "8h": "8h",
    "12h": "12h",
    "1d": "1d",
    "3d": "3d",
    "1w": "1w",
    "1M": "1M"
}

# Performance metrics to track
PERFORMANCE_METRICS = [
    "total_return",
    "annual_return",
    "volatility",
    "sharpe_ratio",
    "sortino_ratio",
    "max_drawdown",
    "calmar_ratio",
    "win_rate",
    "profit_factor",
    "avg_trade_duration",
    "total_trades",
    "execution_time"
]

# Risk management parameters
RISK_PARAMS = {
    "position_sizing": "fixed_fractional",
    "stop_loss_type": "percentage",
    "take_profit_type": "percentage",
    "max_positions": 5,
    "correlation_threshold": 0.7
}
