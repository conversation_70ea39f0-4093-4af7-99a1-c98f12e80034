"""
Global configuration settings for the trading framework.
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Try to import Pydantic, fallback to simple class if not available
try:
    from pydantic import BaseSettings, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    BaseSettings = object
    def Field(default, env=None):
        return default


class Settings:
    """Global settings configuration."""

    def __init__(self):
        # Binance API
        self.binance_api_key = os.getenv("BINANCE_API_KEY", "your_binance_api_key_here")
        self.binance_api_secret = os.getenv("BINANCE_API_SECRET", "your_binance_secret_key_here")
        self.binance_testnet = os.getenv("BINANCE_TESTNET", "True").lower() == "true"

        # Database
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///trading_data.db")
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")

        # Trading
        self.default_symbol = os.getenv("DEFAULT_SYMBOL", "BTCUSDT")
        self.default_interval = os.getenv("DEFAULT_INTERVAL", "1h")
        self.max_concurrent_strategies = int(os.getenv("MAX_CONCURRENT_STRATEGIES", "10"))
        self.backtest_start_date = os.getenv("BACKTEST_START_DATE", "2020-01-01")
        self.backtest_end_date = os.getenv("BACKTEST_END_DATE", "2024-01-01")

        # Performance
        self.enable_jit_compilation = os.getenv("ENABLE_JIT_COMPILATION", "True").lower() == "true"
        self.cache_enabled = os.getenv("CACHE_ENABLED", "True").lower() == "true"
        self.parallel_processing = os.getenv("PARALLEL_PROCESSING", "True").lower() == "true"
        self.max_workers = int(os.getenv("MAX_WORKERS", "8"))

        # Risk Management
        self.max_position_size = float(os.getenv("MAX_POSITION_SIZE", "0.1"))
        self.stop_loss_percentage = float(os.getenv("STOP_LOSS_PERCENTAGE", "0.02"))
        self.take_profit_percentage = float(os.getenv("TAKE_PROFIT_PERCENTAGE", "0.06"))
        self.max_drawdown = float(os.getenv("MAX_DRAWDOWN", "0.15"))

        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = os.getenv("LOG_FILE", "logs/trading.log")


# Global settings instance
settings = Settings()


# Trading intervals mapping
INTERVALS = {
    "1m": "1m",
    "3m": "3m",
    "5m": "5m",
    "15m": "15m",
    "30m": "30m",
    "1h": "1h",
    "2h": "2h",
    "4h": "4h",
    "6h": "6h",
    "8h": "8h",
    "12h": "12h",
    "1d": "1d",
    "3d": "3d",
    "1w": "1w",
    "1M": "1M"
}

# Performance metrics to track
PERFORMANCE_METRICS = [
    "total_return",
    "annual_return",
    "volatility",
    "sharpe_ratio",
    "sortino_ratio",
    "max_drawdown",
    "calmar_ratio",
    "win_rate",
    "profit_factor",
    "avg_trade_duration",
    "total_trades",
    "execution_time"
]

# Risk management parameters
RISK_PARAMS = {
    "position_sizing": "fixed_fractional",
    "stop_loss_type": "percentage",
    "take_profit_type": "percentage",
    "max_positions": 5,
    "correlation_threshold": 0.7
}
