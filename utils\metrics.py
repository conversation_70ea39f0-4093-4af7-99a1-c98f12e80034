"""
Advanced performance metrics and risk calculations.
Optimized for speed with Numba JIT compilation.
"""
import numpy as np
import pandas as pd
from numba import jit
from typing import Tuple, Dict, Any
import warnings
warnings.filterwarnings('ignore')


@jit(nopython=True)
def calculate_sharpe_ratio(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
    """Calculate Sharpe ratio with JIT compilation."""
    if len(returns) == 0:
        return 0.0
    
    excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
    
    if np.std(excess_returns) == 0:
        return 0.0
    
    return np.sqrt(252) * np.mean(excess_returns) / np.std(excess_returns)


@jit(nopython=True)
def calculate_sortino_ratio(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
    """Calculate Sortino ratio (downside deviation)."""
    if len(returns) == 0:
        return 0.0
    
    excess_returns = returns - risk_free_rate / 252
    downside_returns = excess_returns[excess_returns < 0]
    
    if len(downside_returns) == 0 or np.std(downside_returns) == 0:
        return 0.0
    
    downside_std = np.sqrt(np.mean(downside_returns ** 2))
    return np.sqrt(252) * np.mean(excess_returns) / downside_std


@jit(nopython=True)
def calculate_max_drawdown(equity_curve: np.ndarray) -> Tuple[float, int, int]:
    """
    Calculate maximum drawdown and its duration.
    Returns: (max_drawdown, start_index, end_index)
    """
    if len(equity_curve) == 0:
        return 0.0, 0, 0
    
    peak = equity_curve[0]
    max_dd = 0.0
    max_dd_start = 0
    max_dd_end = 0
    current_dd_start = 0
    
    for i in range(len(equity_curve)):
        if equity_curve[i] > peak:
            peak = equity_curve[i]
            current_dd_start = i
        
        drawdown = (peak - equity_curve[i]) / peak
        
        if drawdown > max_dd:
            max_dd = drawdown
            max_dd_start = current_dd_start
            max_dd_end = i
    
    return max_dd, max_dd_start, max_dd_end


@jit(nopython=True)
def calculate_calmar_ratio(returns: np.ndarray, equity_curve: np.ndarray) -> float:
    """Calculate Calmar ratio (Annual return / Max drawdown)."""
    if len(returns) == 0 or len(equity_curve) == 0:
        return 0.0
    
    annual_return = (1 + np.mean(returns)) ** 252 - 1
    max_dd, _, _ = calculate_max_drawdown(equity_curve)
    
    if max_dd == 0:
        return 0.0
    
    return annual_return / max_dd


@jit(nopython=True)
def calculate_var(returns: np.ndarray, confidence_level: float = 0.05) -> float:
    """Calculate Value at Risk."""
    if len(returns) == 0:
        return 0.0
    
    sorted_returns = np.sort(returns)
    index = int(confidence_level * len(sorted_returns))
    
    if index >= len(sorted_returns):
        return sorted_returns[-1]
    
    return sorted_returns[index]


@jit(nopython=True)
def calculate_cvar(returns: np.ndarray, confidence_level: float = 0.05) -> float:
    """Calculate Conditional Value at Risk (Expected Shortfall)."""
    if len(returns) == 0:
        return 0.0
    
    var = calculate_var(returns, confidence_level)
    tail_returns = returns[returns <= var]
    
    if len(tail_returns) == 0:
        return var
    
    return np.mean(tail_returns)


@jit(nopython=True)
def calculate_omega_ratio(returns: np.ndarray, threshold: float = 0.0) -> float:
    """Calculate Omega ratio."""
    if len(returns) == 0:
        return 0.0
    
    excess_returns = returns - threshold
    positive_returns = excess_returns[excess_returns > 0]
    negative_returns = excess_returns[excess_returns < 0]
    
    if len(negative_returns) == 0:
        return np.inf if len(positive_returns) > 0 else 1.0
    
    if len(positive_returns) == 0:
        return 0.0
    
    return np.sum(positive_returns) / abs(np.sum(negative_returns))


@jit(nopython=True)
def calculate_information_ratio(returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
    """Calculate Information ratio."""
    if len(returns) == 0 or len(benchmark_returns) == 0:
        return 0.0
    
    min_length = min(len(returns), len(benchmark_returns))
    active_returns = returns[:min_length] - benchmark_returns[:min_length]
    
    if np.std(active_returns) == 0:
        return 0.0
    
    return np.sqrt(252) * np.mean(active_returns) / np.std(active_returns)


class AdvancedMetrics:
    """Advanced performance metrics calculator."""
    
    @staticmethod
    def calculate_all_metrics(
        returns: np.ndarray,
        equity_curve: np.ndarray,
        benchmark_returns: np.ndarray = None,
        risk_free_rate: float = 0.02
    ) -> Dict[str, float]:
        """Calculate comprehensive set of performance metrics."""
        
        if len(returns) == 0:
            return {metric: 0.0 for metric in [
                'total_return', 'annual_return', 'volatility', 'sharpe_ratio',
                'sortino_ratio', 'calmar_ratio', 'max_drawdown', 'var_5',
                'cvar_5', 'omega_ratio', 'skewness', 'kurtosis'
            ]}
        
        # Basic metrics
        total_return = (equity_curve[-1] - equity_curve[0]) / equity_curve[0] if len(equity_curve) > 0 else 0
        annual_return = (1 + np.mean(returns)) ** 252 - 1
        volatility = np.std(returns) * np.sqrt(252)
        
        # Risk-adjusted metrics
        sharpe = calculate_sharpe_ratio(returns, risk_free_rate)
        sortino = calculate_sortino_ratio(returns, risk_free_rate)
        calmar = calculate_calmar_ratio(returns, equity_curve)
        
        # Drawdown metrics
        max_dd, _, _ = calculate_max_drawdown(equity_curve)
        
        # Risk metrics
        var_5 = calculate_var(returns, 0.05)
        cvar_5 = calculate_cvar(returns, 0.05)
        omega = calculate_omega_ratio(returns)
        
        # Distribution metrics
        skewness = AdvancedMetrics._calculate_skewness(returns)
        kurtosis = AdvancedMetrics._calculate_kurtosis(returns)
        
        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe,
            'sortino_ratio': sortino,
            'calmar_ratio': calmar,
            'max_drawdown': max_dd,
            'var_5': var_5,
            'cvar_5': cvar_5,
            'omega_ratio': omega,
            'skewness': skewness,
            'kurtosis': kurtosis
        }
        
        # Add benchmark comparison if provided
        if benchmark_returns is not None:
            info_ratio = calculate_information_ratio(returns, benchmark_returns)
            metrics['information_ratio'] = info_ratio
            
            # Beta calculation
            if len(benchmark_returns) >= len(returns):
                beta = AdvancedMetrics._calculate_beta(returns, benchmark_returns[:len(returns)])
                metrics['beta'] = beta
                
                # Alpha calculation
                benchmark_annual = (1 + np.mean(benchmark_returns[:len(returns)])) ** 252 - 1
                alpha = annual_return - (risk_free_rate + beta * (benchmark_annual - risk_free_rate))
                metrics['alpha'] = alpha
        
        return metrics
    
    @staticmethod
    @jit(nopython=True)
    def _calculate_skewness(returns: np.ndarray) -> float:
        """Calculate skewness of returns."""
        if len(returns) < 3:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        skew = np.mean(((returns - mean_return) / std_return) ** 3)
        return skew
    
    @staticmethod
    @jit(nopython=True)
    def _calculate_kurtosis(returns: np.ndarray) -> float:
        """Calculate kurtosis of returns."""
        if len(returns) < 4:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        kurt = np.mean(((returns - mean_return) / std_return) ** 4) - 3
        return kurt
    
    @staticmethod
    @jit(nopython=True)
    def _calculate_beta(returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
        """Calculate beta relative to benchmark."""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 1.0
        
        covariance = np.cov(returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance == 0:
            return 1.0
        
        return covariance / benchmark_variance
    
    @staticmethod
    def calculate_rolling_metrics(
        returns: pd.Series,
        window: int = 252,
        metrics: list = None
    ) -> pd.DataFrame:
        """Calculate rolling performance metrics."""
        
        if metrics is None:
            metrics = ['sharpe_ratio', 'volatility', 'max_drawdown']
        
        rolling_metrics = pd.DataFrame(index=returns.index)
        
        for metric in metrics:
            if metric == 'sharpe_ratio':
                rolling_metrics[metric] = returns.rolling(window).apply(
                    lambda x: calculate_sharpe_ratio(x.values), raw=False
                )
            elif metric == 'volatility':
                rolling_metrics[metric] = returns.rolling(window).std() * np.sqrt(252)
            elif metric == 'max_drawdown':
                # Calculate rolling max drawdown
                equity = (1 + returns).cumprod()
                rolling_max = equity.rolling(window).max()
                rolling_dd = (equity - rolling_max) / rolling_max
                rolling_metrics[metric] = rolling_dd.rolling(window).min().abs()
        
        return rolling_metrics
    
    @staticmethod
    def calculate_trade_metrics(trades: pd.DataFrame) -> Dict[str, float]:
        """Calculate trade-specific metrics."""
        
        if trades.empty:
            return {}
        
        # Basic trade metrics
        total_trades = len(trades)
        winning_trades = len(trades[trades['pnl'] > 0])
        losing_trades = len(trades[trades['pnl'] < 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # P&L metrics
        total_pnl = trades['pnl'].sum()
        avg_win = trades[trades['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades[trades['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        # Profit factor
        gross_profit = trades[trades['pnl'] > 0]['pnl'].sum()
        gross_loss = abs(trades[trades['pnl'] < 0]['pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        # Duration metrics
        if 'duration' in trades.columns:
            avg_duration = trades['duration'].mean()
            avg_winning_duration = trades[trades['pnl'] > 0]['duration'].mean() if winning_trades > 0 else pd.Timedelta(0)
            avg_losing_duration = trades[trades['pnl'] < 0]['duration'].mean() if losing_trades > 0 else pd.Timedelta(0)
        else:
            avg_duration = avg_winning_duration = avg_losing_duration = pd.Timedelta(0)
        
        # Consecutive metrics
        consecutive_wins = AdvancedMetrics._calculate_consecutive_wins(trades['pnl'].values)
        consecutive_losses = AdvancedMetrics._calculate_consecutive_losses(trades['pnl'].values)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_duration_hours': avg_duration.total_seconds() / 3600 if avg_duration else 0,
            'avg_winning_duration_hours': avg_winning_duration.total_seconds() / 3600 if avg_winning_duration else 0,
            'avg_losing_duration_hours': avg_losing_duration.total_seconds() / 3600 if avg_losing_duration else 0,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'largest_win': trades['pnl'].max(),
            'largest_loss': trades['pnl'].min(),
            'expectancy': total_pnl / total_trades if total_trades > 0 else 0
        }
    
    @staticmethod
    @jit(nopython=True)
    def _calculate_consecutive_wins(pnl_array: np.ndarray) -> int:
        """Calculate maximum consecutive wins."""
        max_consecutive = 0
        current_consecutive = 0
        
        for pnl in pnl_array:
            if pnl > 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    @staticmethod
    @jit(nopython=True)
    def _calculate_consecutive_losses(pnl_array: np.ndarray) -> int:
        """Calculate maximum consecutive losses."""
        max_consecutive = 0
        current_consecutive = 0
        
        for pnl in pnl_array:
            if pnl < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
