# 🚀 Ultra-Fast Cryptocurrency Trading Framework

Um framework de trading de criptomoedas extremamente otimizado, focado em **velocidade de execução**, **robustez real** e **evolução iterativa** de estratégias com dados históricos da Binance.

## 🎯 Características Principais

### ⚡ Performance Extrema
- **Compilação JIT** com Numba para cálculos críticos
- **Operações vetorizadas** com NumPy e Pandas otimizados
- **Cache inteligente** com Redis para dados frequentes
- **Processamento paralelo** para otimização de estratégias
- **Tempo de execução** < 1ms para geração de sinais

### 🛡️ Robustez Real
- **Walk-forward analysis** para validação temporal
- **Análise de robustez** com múltiplos períodos
- **Gestão de risco** integrada e configurável
- **Tratamento de erros** robusto para ambiente de produção
- **Validação de dados** em tempo real

### 🧬 Evolução Iterativa
- **Otimização genética** para descoberta de parâmetros
- **Otimização Bayesiana** com Optuna
- **Comparação automática** de performance
- **Tracking histórico** de todas as métricas
- **Melhoria contínua** baseada em resultados

### 📊 Análise Avançada
- **15+ métricas** de performance (Sharpe, Sortino, Calmar, etc.)
- **Análise de drawdown** detalhada
- **Correlação entre estratégias**
- **Dashboard interativo** com Plotly
- **Relatórios automáticos** de performance

## 🏗️ Arquitetura

```
trading-framework/
├── core/                    # Motor principal
│   ├── data_manager.py     # Gestão de dados Binance
│   ├── backtester.py       # Engine de backtesting
│   └── optimizer.py        # Otimizador de estratégias
├── strategies/             # Estratégias de trading
│   ├── base_strategy.py    # Classe base
│   ├── technical_strategies.py  # Estratégias técnicas
│   └── ml_strategies.py    # Estratégias ML (futuro)
├── utils/                  # Utilitários
│   ├── performance_tracker.py  # Tracking de performance
│   ├── risk_management.py  # Gestão de risco
│   └── metrics.py         # Métricas customizadas
├── config/                 # Configurações
│   └── settings.py        # Configurações globais
└── main.py                # Script principal
```

## 🚀 Instalação Rápida

### 1. Clone o repositório
```bash
git clone <repository-url>
cd trading-framework
```

### 2. Instale as dependências
```bash
pip install -r requirements.txt
```

### 3. Configure as variáveis de ambiente
Edite o arquivo `.env` com suas credenciais da Binance:
```env
BINANCE_API_KEY=sua_api_key_aqui
BINANCE_API_SECRET=sua_secret_key_aqui
```

### 4. Execute o framework
```bash
python main.py
```

## 📈 Estratégias Implementadas

### 1. Moving Average Crossover
- **Otimizada** com TA-Lib para máxima velocidade
- **Filtro de tendência** para reduzir falsos sinais
- **Confirmação de volume** opcional

### 2. RSI Mean Reversion
- **Thresholds dinâmicos** baseados em volatilidade
- **Filtro de tendência** configurável
- **Scaling de posição** baseado em extremidade do RSI

### 3. Bollinger Bands Momentum
- **Breakouts com confirmação** de volume
- **Mean reversion** em bandas
- **Análise de momentum** integrada

### 4. MACD Strategy
- **Crossovers de linha de sinal**
- **Análise de histograma** para timing
- **Compilação JIT** para cálculos rápidos

## ⚙️ Configuração Avançada

### Performance Settings
```python
# config/settings.py
ENABLE_JIT_COMPILATION = True
CACHE_ENABLED = True
PARALLEL_PROCESSING = True
MAX_WORKERS = 8
```

### Risk Management
```python
MAX_POSITION_SIZE = 0.1        # 10% do capital por posição
STOP_LOSS_PERCENTAGE = 0.02    # 2% stop loss
TAKE_PROFIT_PERCENTAGE = 0.06  # 6% take profit
MAX_DRAWDOWN = 0.15           # 15% drawdown máximo
```

## 🔬 Exemplo de Uso

### Teste Básico de Estratégia
```python
from core.data_manager import DataManager
from core.backtester import VectorizedBacktester
from strategies.technical_strategies import MovingAverageCrossover

# Inicializar componentes
data_manager = DataManager()
backtester = VectorizedBacktester(initial_capital=10000)

# Obter dados históricos
data = data_manager.get_historical_data(
    symbol="BTCUSDT",
    interval="1h",
    start_date="2023-01-01",
    end_date="2024-01-01"
)

# Criar e testar estratégia
strategy = MovingAverageCrossover(
    symbol="BTCUSDT",
    timeframe="1h",
    fast_period=10,
    slow_period=30
)

result = backtester.run_backtest(strategy, data)

print(f"Total Return: {result.total_return:.2%}")
print(f"Sharpe Ratio: {result.sharpe_ratio:.4f}")
print(f"Max Drawdown: {result.max_drawdown:.2%}")
```

### Otimização de Estratégia
```python
from core.optimizer import StrategyOptimizer

optimizer = StrategyOptimizer(data_manager, backtester)

# Definir ranges de parâmetros
parameter_ranges = {
    'fast_period': (5, 20),
    'slow_period': (20, 50),
    'trend_period': (50, 200)
}

# Otimizar estratégia
optimization_result = optimizer.optimize_strategy(
    strategy_class=MovingAverageCrossover,
    symbol="BTCUSDT",
    timeframe="1h",
    parameter_ranges=parameter_ranges,
    start_date="2023-01-01",
    end_date="2024-01-01",
    method='bayesian',
    n_trials=100
)

print(f"Best Parameters: {optimization_result.best_parameters}")
print(f"Best Score: {optimization_result.best_score:.4f}")
print(f"Robustness Score: {optimization_result.robustness_score:.4f}")
```

## 📊 Métricas de Performance

O framework calcula automaticamente:

- **Return Metrics**: Total Return, Annual Return
- **Risk Metrics**: Sharpe Ratio, Sortino Ratio, Calmar Ratio
- **Drawdown Analysis**: Max Drawdown, Average Drawdown
- **Trade Metrics**: Win Rate, Profit Factor, Average Trade Duration
- **Execution Metrics**: Signal Generation Time, Backtest Speed
- **Robustness Metrics**: Walk-Forward Performance, Parameter Sensitivity

## 🔧 Extensibilidade

### Criar Nova Estratégia
```python
from strategies.base_strategy import BaseStrategy, Signal

class MinhaEstrategia(BaseStrategy):
    def __init__(self, symbol, timeframe, meu_parametro=10):
        super().__init__(
            name="MinhaEstrategia",
            symbol=symbol,
            timeframe=timeframe,
            parameters={'meu_parametro': meu_parametro}
        )
        self.meu_parametro = meu_parametro
    
    def generate_signal(self, data):
        # Sua lógica aqui
        return Signal(
            timestamp=data.index[-1],
            action='BUY',  # 'BUY', 'SELL', 'HOLD'
            price=data['close'].iloc[-1],
            confidence=0.8,
            metadata={'custom_info': 'valor'}
        )
    
    def calculate_position_size(self, signal, account_balance):
        return account_balance * 0.1  # 10% do capital
```

## 🚀 Performance Benchmarks

Em testes com dados históricos de 2 anos (BTCUSDT 1h):

- **Geração de sinal**: < 1ms
- **Backtest completo**: < 5s para 17,520 pontos
- **Otimização (100 trials)**: < 2 minutos
- **Walk-forward analysis**: < 30s

## 🛡️ Gestão de Risco

- **Position Sizing**: Fixed Fractional, Kelly Criterion
- **Stop Loss**: Percentage, ATR-based, Trailing
- **Take Profit**: Fixed, Dynamic, Scaling Out
- **Portfolio Risk**: Correlation Analysis, Exposure Limits
- **Drawdown Control**: Dynamic Position Sizing

## 📈 Roadmap

### Próximas Funcionalidades
- [ ] Estratégias de Machine Learning
- [ ] Trading de múltiplos símbolos
- [ ] Análise de sentimento
- [ ] Integração com mais exchanges
- [ ] API REST para controle remoto
- [ ] Dashboard web em tempo real

### Otimizações Futuras
- [ ] GPU acceleration com CuPy
- [ ] Distributed computing com Dask
- [ ] Real-time streaming com WebSockets
- [ ] Advanced portfolio optimization

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## ⚠️ Disclaimer

Este framework é para fins educacionais e de pesquisa. Trading de criptomoedas envolve riscos significativos. Sempre teste estratégias em ambiente de simulação antes de usar capital real.

## 📄 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

---

**🎯 Objetivo**: Criar as melhores estratégias de trading do mundo através de evolução iterativa, comparação robusta e otimização extrema de performance.

**⚡ Foco**: Velocidade de execução, robustez real e melhoria contínua.
